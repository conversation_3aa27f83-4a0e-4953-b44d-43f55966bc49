import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View, Text, Alert, Switch, StyleSheet, TouchableOpacity, Animated, Easing, Image, Linking } from 'react-native'; // Added Linking
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import Constants from 'expo-constants'; // Import Constants
import { Ionicons } from '@expo/vector-icons';
import { auth } from '../firebaseConfig';
import { signOut } from 'firebase/auth';
// Removed RNPickerSelect import as it's no longer used for language
import Slider from '@react-native-community/slider'; // Import Slider
import { useTranslation } from 'react-i18next';
import i18n from 'i18next'; // Keep this if used elsewhere, or rely on the hook's instance
 import { initReactI18next } from 'react-i18next';
 import { useNavigation, useFocusEffect } from '@react-navigation/native';

 import BannerAdComponent from '../components/BannerAdComponent';
 import { AD_PLACEMENTS } from '../src/config/adConfig';
 // Removed redundant languageResources and i18n.init() call
 // The app now relies solely on the initialization in screens/localization.js

 // Define base font sizes *outside* the component
 const BASE_FONT_SIZES = {
  title: 24,
  sectionTitle: 16,
  profileName: 17,
  profileEmail: 14,
  actionButtonText: 16,
  optionText: 16,
  fontSizeButtonText: 14,
  logoutButtonText: 16,
  appVersion: 14,
};

// Define darkMode as a constant outside the component
const DARK_MODE = false;

// Helper function for picker styles (remains outside)
const pickerSelectStyles = (isDarkMode) => StyleSheet.create({
  inputIOS: {
    fontSize: 16,
    paddingVertical: 8,
    paddingHorizontal: 10,
    color: isDarkMode ? '#fff' : '#333',
    flex: 1,
  },
  inputAndroid: {
    fontSize: 16,
    paddingHorizontal: 10,
    paddingVertical: 8,
    color: isDarkMode ? '#fff' : '#333',
    flex: 1,
  },
  viewContainer: {
    flex: 1,
    marginLeft: 10,
    justifyContent: 'center',
  },
  iconContainer: {
    top: '50%',
    marginTop: -10,
    right: 10,
  },
  placeholder: {
     color: isDarkMode ? '#aaa' : '#999',
  }
});

// Define language button styles outside the component for clarity
const languageButtonStyles = (isDarkMode, isSelected) => ({
  button: {
    flex: 1, // Make buttons share space
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: isSelected ? (isDarkMode ? '#81b0ff' : '#007bff') : (isDarkMode ? '#555' : '#ccc'),
    backgroundColor: isSelected ? (isDarkMode ? '#81b0ff' : '#007bff') : 'transparent',
    alignItems: 'center', // Center text
    marginHorizontal: 5, // Add spacing between buttons
  },
  text: {
    color: isSelected ? '#fff' : (isDarkMode ? '#eee' : '#333'),
    fontWeight: isSelected ? 'bold' : 'normal',
    fontSize: 14, // Adjust font size if needed
  },
});


const SettingsScreen = () => {
  // Use the global i18n instance imported via 'i18next' or './localization'
  // Ensure the correct import is used if needed, but useTranslation should work with the global instance
  const { t, i18n: i18nInstance } = useTranslation(); // Get i18n instance from hook
  // Simple state without dark mode
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [soundVolume, setSoundVolume] = useState(0.5);
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState(1);
  // Local state for smoother slider interaction
  const [localVolume, setLocalVolume] = useState(soundVolume);
  const navigation = useNavigation();
  const [userData, setUserData] = useState({
    email: auth.currentUser?.email,
    displayName: auth.currentUser?.displayName,
  });
  const [profileImageUri, setProfileImageUri] = useState(null);
  // Initialize language state from the i18n instance provided by the hook
  const [language, setLanguage] = useState(i18nInstance.language);

  const PROFILE_IMAGE_KEY = '@profileImageUri';

  const fadeAnim = useRef(new Animated.Value(0)).current;
  // Array size for animated rows (without notifications)
  const rowsAnim = useRef([...Array(7)].map(() => new Animated.Value(0))).current; // Reduced size to 7
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, { toValue: 1, duration: 500, useNativeDriver: true }).start();
  }, [fadeAnim]);

  useEffect(() => {
     Animated.stagger(100, rowsAnim.map((anim) =>
       Animated.timing(anim, { toValue: 1, duration: 350, easing: Easing.out(Easing.ease), useNativeDriver: true })
     )).start();
  }, [rowsAnim]);

  useFocusEffect(
    useCallback(() => {
      const loadData = async () => {
        const currentUser = auth.currentUser;
        if (currentUser) {
          try {
            await currentUser.reload();
            const freshUser = auth.currentUser;
            setUserData({ email: freshUser?.email, displayName: freshUser?.displayName });
          } catch (error) {
            console.error("Error reloading user data:", error);
            setUserData({ email: currentUser.email, displayName: currentUser.displayName });
          }
        }
        try {
          const savedUri = await AsyncStorage.getItem(PROFILE_IMAGE_KEY);
          if (savedUri) {
            const fileInfo = await FileSystem.getInfoAsync(savedUri);
            if (fileInfo.exists) {
              setProfileImageUri(savedUri);
            } else {
              await AsyncStorage.removeItem(PROFILE_IMAGE_KEY);
              setProfileImageUri(null);
            }
          } else {
            setProfileImageUri(null);
          }
        } catch (e) {
          console.error("Failed to load profile image URI:", e);
          setProfileImageUri(null);
        }
      };
      loadData();
      return () => {};
    }, [])
  );

  // Sync localVolume when global soundVolume changes (e.g., on initial load)
  useEffect(() => {
    setLocalVolume(soundVolume);
  }, [soundVolume]);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem('language');
        if (storedLanguage && storedLanguage !== i18nInstance.language) { // Check if different from current
          // Only change if necessary and update state
          setLanguage(storedLanguage);
          i18nInstance.changeLanguage(storedLanguage);
        } else if (!storedLanguage) {
          // If nothing stored, ensure state matches the default loaded language
          setLanguage(i18nInstance.language);
        }

      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };
    loadSettings();
  }, []);

  const changeLanguage = async (lang) => {
    if (!lang || lang === language) return; // Don't change if same language selected
    try {
      setLanguage(lang); // Update local state
      await i18nInstance.changeLanguage(lang); // Change language using the instance from the hook
      await AsyncStorage.setItem('language', lang); // Save preference
      Alert.alert(t('language_changed'), `${t('app_language_set')} ${lang}. ${t('restart_to_apply')}`);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };



  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigation.reset({ index: 0, routes: [{ name: 'Login' }] });
    } catch (error) {
      console.error('Logout Error:', error);
      Alert.alert(t('logout_failed'), t('logout_error'));
    }
  };

  // Add missing functions for settings functionality
  const changeFontSize = (size) => {
    setFontSizeMultiplier(size);
  };

  const toggleSound = () => {
    setSoundEnabled(prev => !prev);
  };

  const changeSoundVolume = (volume) => {
    setSoundVolume(volume);
  };

  const animatedRowStyle = (index) => ({
     opacity: rowsAnim[index] || 1,
     transform: [{
       translateY: (rowsAnim[index] || new Animated.Value(1)).interpolate({ inputRange: [0, 1], outputRange: [20, 0] })
     }],
  });

  const currentPickerStyles = pickerSelectStyles(DARK_MODE);
  // Call styles function inside the component
  const dynamicStyles = styles(DARK_MODE, fontSizeMultiplier);

  return (
    <Animated.ScrollView
      style={[dynamicStyles.baseScrollView, { opacity: fadeAnim }]}
      contentContainerStyle={[dynamicStyles.scrollContainer, DARK_MODE ? dynamicStyles.darkBackground : dynamicStyles.lightBackground]}
    >
      <Text style={[dynamicStyles.title, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>{t('settings')}</Text>

      {/* --- Account Section --- */}
      <Text style={[dynamicStyles.sectionTitle, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>Account</Text>
      <View style={[dynamicStyles.sectionContainer, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
        <View style={dynamicStyles.profileInfoRow}>
          <Image
            source={profileImageUri ? { uri: profileImageUri } : require('../assets/images/icon.png')}
            style={dynamicStyles.profilePic}
          />
          <View style={dynamicStyles.profileTextContainer}>
            <Text style={[dynamicStyles.profileName, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>
              {userData.displayName || 'User'}
            </Text>
            <Text style={[dynamicStyles.profileEmail, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText, { marginLeft: 0 }]}>
              {userData.email || 'No email found'}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={[dynamicStyles.actionButton, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]} onPress={() => navigation.navigate('ChangePassword')}>
           <Text style={[dynamicStyles.actionButtonText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>Change Password</Text>
           <Ionicons name="chevron-forward" size={20} color={DARK_MODE ? '#aaa' : '#666'} />
        </TouchableOpacity>
      </View>

      {/* --- Appearance Section --- */}
      <Text style={[dynamicStyles.sectionTitle, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>Appearance</Text>
      <View style={[dynamicStyles.sectionContainer, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
        <Animated.View style={animatedRowStyle(0)}>
           <View style={[dynamicStyles.optionRow, { borderTopWidth: 0 }, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
             <Ionicons name="text" size={24} color={DARK_MODE ? '#fff' : '#333'} />
             <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>Font Size</Text>
             <View style={dynamicStyles.fontSizeSelector}>
                <TouchableOpacity
                  style={[dynamicStyles.fontSizeButton, fontSizeMultiplier === 0.85 ? dynamicStyles.fontSizeButtonSelected : {}]}
                  onPress={() => changeFontSize(0.85)}
                >
                   <Text style={[dynamicStyles.fontSizeButtonText, fontSizeMultiplier === 0.85 ? dynamicStyles.fontSizeButtonTextSelected : {}]}>S</Text>
                </TouchableOpacity>
                 <TouchableOpacity
                  style={[dynamicStyles.fontSizeButton, fontSizeMultiplier === 1.0 ? dynamicStyles.fontSizeButtonSelected : {}]}
                  onPress={() => changeFontSize(1.0)}
                 >
                   <Text style={[dynamicStyles.fontSizeButtonText, fontSizeMultiplier === 1.0 ? dynamicStyles.fontSizeButtonTextSelected : {}]}>M</Text>
                </TouchableOpacity>
                 <TouchableOpacity
                  style={[dynamicStyles.fontSizeButton, fontSizeMultiplier === 1.15 ? dynamicStyles.fontSizeButtonSelected : {}]}
                  onPress={() => changeFontSize(1.15)}
                 >
                   <Text style={[dynamicStyles.fontSizeButtonText, fontSizeMultiplier === 1.15 ? dynamicStyles.fontSizeButtonTextSelected : {}]}>L</Text>
                </TouchableOpacity>
             </View>
           </View>
        </Animated.View>
      </View>

      {/* --- General Section --- */}
      <Text style={[dynamicStyles.sectionTitle, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>General</Text>
      <View style={[dynamicStyles.sectionContainer, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
        <Animated.View style={animatedRowStyle(2)}>
          <View style={[dynamicStyles.optionRow, { borderTopWidth: 0 }, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
            <Ionicons name="language" size={24} color={DARK_MODE ? '#fff' : '#333'} />
            <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>{t('language')}</Text>
            {/* Language Buttons Container */}
            <View style={dynamicStyles.languageButtonContainer}>
              <TouchableOpacity
                style={languageButtonStyles(DARK_MODE, language === 'en').button}
                onPress={() => changeLanguage('en')}
              >
                <Text style={languageButtonStyles(DARK_MODE, language === 'en').text}>English</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={languageButtonStyles(DARK_MODE, language === 'ta').button}
                onPress={() => changeLanguage('ta')}
              >
                <Text style={languageButtonStyles(DARK_MODE, language === 'ta').text}>தமிழ்</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

         <Animated.View style={animatedRowStyle(3)}>
           <View style={[dynamicStyles.optionRow, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
             <Ionicons name={soundEnabled ? "volume-high" : "volume-mute"} size={24} color={DARK_MODE ? '#fff' : '#333'} />
             <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>Sound Effects</Text>
             <Switch value={soundEnabled} onValueChange={toggleSound} trackColor={{ false: "#767577", true: "#81b0ff" }} thumbColor={soundEnabled ? "#f4f3f4" : "#f4f3f4"}/>
           </View>
        </Animated.View>
        {/* Sound Volume Slider */}
        <Animated.View style={animatedRowStyle(4)}>
           <View style={[dynamicStyles.optionRow, dynamicStyles.sliderRow, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
             <Ionicons name="volume-medium" size={24} color={DARK_MODE ? '#fff' : '#333'} />
             <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>{t('sound_volume')}</Text>
             <Slider
               style={[dynamicStyles.slider, {width: 200}]} // Added fixed width for testing
                minimumValue={0}
                maximumValue={1}
                value={localVolume} // Use local state for immediate visual feedback
                onValueChange={setLocalVolume} // Update local state during drag
                onSlidingComplete={() => changeSoundVolume(localVolume)} // Update global state on release
                minimumTrackTintColor={DARK_MODE ? "#6495ED" : "#4682B4"} // CornflowerBlue / SteelBlue - Softer blues
                maximumTrackTintColor={DARK_MODE ? "#444" : "#d3d3d3"} // Darker gray / LightGray
                thumbTintColor={DARK_MODE ? "#A9A9A9" : "#808080"} // DarkGray / Gray - Less stark thumb
                disabled={!soundEnabled} // Disable slider if sound is off
              />
            </View>
        </Animated.View>
      </View>

      {/* --- About Section --- */}
      <Text style={[dynamicStyles.sectionTitle, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>About</Text>
      <View style={[dynamicStyles.sectionContainer, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
         {/* App Version Row */}
         <Animated.View style={animatedRowStyle(5)}> {/* Adjusted index */}
             <View style={[dynamicStyles.optionRow, { borderTopWidth: 0 }, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
                 <Ionicons name="information-circle-outline" size={24} color={DARK_MODE ? '#fff' : '#333'} />
                 <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>App Version</Text>
                 {/* Read version dynamically from Constants */}
                 <Text style={[dynamicStyles.appVersion, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>
                   {Constants.expoConfig?.version ?? 'N/A'}
                 </Text>
               </View>
           </Animated.View>
           {/* Privacy Policy Row */}
         <Animated.View style={animatedRowStyle(6)}> {/* Adjusted index */}
           <TouchableOpacity
              style={[dynamicStyles.optionRow, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}
              onPress={async () => {
                const url = 'https://sites.google.com/view/beetech-privacy-policy/home';
                try {
                  const supported = await Linking.canOpenURL(url);
                  if (supported) {
                    await Linking.openURL(url);
                  } else {
                    Alert.alert(`Don't know how to open this URL: ${url}`);
                  }
                } catch (error) {
                  console.error("Failed to open URL:", error);
                  Alert.alert("Error", "Could not open the privacy policy link.");
                }
              }}
            >
              <Ionicons name="shield-checkmark-outline" size={24} color={DARK_MODE ? '#fff' : '#333'} />
             <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>Privacy Policy</Text>
             <Ionicons name="chevron-forward" size={20} color={DARK_MODE ? '#aaa' : '#666'} />
           </TouchableOpacity>
         </Animated.View>
      </View>

      {/* --- Support Section --- */}
      <Text style={[dynamicStyles.sectionTitle, DARK_MODE ? dynamicStyles.darkSubText : dynamicStyles.lightSubText]}>Support</Text>
      <View style={[dynamicStyles.sectionContainer, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}>
         <Animated.View style={animatedRowStyle(6)}> {/* Adjusted index */}
           <TouchableOpacity
             style={[dynamicStyles.optionRow, { borderTopWidth: 0 }, DARK_MODE ? dynamicStyles.darkBorder : dynamicStyles.lightBorder]}
             onPress={async () => { // Revert to mailto link functionality
               const recipient = '<EMAIL>';
               const subject = 'Quiz Bee App Feedback';
               const mailtoUrl = `mailto:${recipient}?subject=${encodeURIComponent(subject)}`;
               try {
                 const supported = await Linking.canOpenURL(mailtoUrl);
                 if (supported) {
                   await Linking.openURL(mailtoUrl);
                 } else {
                   Alert.alert("Error", "Could not open email app.");
                 }
               } catch (error) {
                 console.error("Failed to open mailto link:", error);
                 Alert.alert("Error", "Could not open email app.");
               }
             }}
           >
             <Ionicons name="mail-outline" size={24} color={DARK_MODE ? '#fff' : '#333'} /> {/* Revert icon */}
             <Text style={[dynamicStyles.optionText, DARK_MODE ? dynamicStyles.darkText : dynamicStyles.lightText]}>Send Feedback</Text>
             <Ionicons name="chevron-forward" size={20} color={DARK_MODE ? '#aaa' : '#666'} />
           </TouchableOpacity>
         </Animated.View>
      </View>




      <TouchableOpacity
        activeOpacity={0.8}
        onPressIn={() => Animated.spring(scaleAnim, { toValue: 0.95, useNativeDriver: true }).start()}
        onPressOut={() => Animated.spring(scaleAnim, { toValue: 1, useNativeDriver: true }).start()}
        onPress={handleLogout}
      >
        <Animated.View style={[dynamicStyles.logoutButton, { transform: [{ scale: scaleAnim }] }]}>
          <Ionicons name="log-out-outline" size={24} color="#fff" style={dynamicStyles.logoutIcon} />
          <Text style={dynamicStyles.logoutButtonText}>{t('logout')}</Text>
        </Animated.View>
      </TouchableOpacity>

      {/* Banner Ad */}
      <View style={dynamicStyles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
        />
      </View>
    </Animated.ScrollView>
  );
};

// Styles function to adapt to dark mode and font size
const styles = (isDarkMode, fontSizeMultiplier) => StyleSheet.create({
  baseScrollView: { flex: 1 },
  scrollContainer: { paddingHorizontal: 15, paddingVertical: 20, paddingBottom: 50 }, // Adjusted padding
  darkBackground: { backgroundColor: '#000' }, // Main background
  lightBackground: { backgroundColor: '#f4f4f8' }, // Slightly softer light background
  title: { fontWeight: 'bold', textAlign: 'center', marginBottom: 30, fontSize: BASE_FONT_SIZES.title * fontSizeMultiplier }, // Increased bottom margin
  sectionTitle: { fontWeight: '600', marginTop: 25, marginBottom: 12, marginLeft: 5, textTransform: 'uppercase', fontSize: BASE_FONT_SIZES.sectionTitle * fontSizeMultiplier, color: isDarkMode ? '#aaa' : '#666' }, // Adjusted margins and color
  sectionContainer: { borderRadius: 12, marginBottom: 25, borderWidth: StyleSheet.hairlineWidth, overflow: 'hidden', backgroundColor: isDarkMode ? '#1c1c1e' : '#fff' }, // Increased marginBottom, slightly larger borderRadius
  profileInfoRow: { flexDirection: 'row', alignItems: 'center', paddingVertical: 20, paddingHorizontal: 15 }, // Increased padding
  actionButton: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 15, paddingHorizontal: 15, borderTopWidth: StyleSheet.hairlineWidth }, // Consistent padding
  actionButtonText: { fontSize: BASE_FONT_SIZES.actionButtonText * fontSizeMultiplier },
  darkBorder: { borderColor: isDarkMode ? '#38383a' : '#e5e5ea' }, // Adjusted border colors
  lightBorder: { borderColor: isDarkMode ? '#38383a' : '#e5e5ea' }, // Adjusted border colors
  profilePic: {
    width: 60, // Slightly smaller profile pic
    height: 60,
    borderRadius: 30,
    marginRight: 15,
    backgroundColor: isDarkMode ? '#333' : '#eee', // Adjusted background
    borderWidth: 1,
    borderColor: isDarkMode ? '#444' : '#ddd', // Adjusted border
  },
  profileTextContainer: { flex: 1, justifyContent: 'center' },
  profileName: { fontWeight: '600', marginBottom: 3, fontSize: BASE_FONT_SIZES.profileName * fontSizeMultiplier }, // Slightly more margin
  profileEmail: { fontSize: BASE_FONT_SIZES.profileEmail * fontSizeMultiplier, color: isDarkMode ? '#aaa' : '#555' }, // Adjusted email color
  optionRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 15, paddingHorizontal: 15, borderTopWidth: StyleSheet.hairlineWidth }, // Consistent padding
  optionText: { flexShrink: 1, marginRight: 15, marginLeft: 15, fontSize: BASE_FONT_SIZES.optionText * fontSizeMultiplier }, // Adjusted margins
  languageButtonContainer: { flexDirection: 'row', flexGrow: 1, justifyContent: 'flex-end' }, // Container for buttons
  darkText: { color: '#E1E1E1' }, // Slightly off-white for dark mode text
  lightText: { color: '#1C1C1E' }, // Slightly off-black for light mode text
  darkSubText: { color: '#aaa' },
  lightSubText: { color: '#666' },
  fontSizeSelector: { flexDirection: 'row', alignItems: 'center' },
  fontSizeButton: { paddingHorizontal: 12, paddingVertical: 6, borderRadius: 6, borderWidth: 1, borderColor: isDarkMode ? '#555' : '#ccc', marginLeft: 8 }, // Adjusted border color
  fontSizeButtonSelected: { backgroundColor: isDarkMode ? '#0A84FF' : '#007AFF', borderColor: isDarkMode ? '#0A84FF' : '#007AFF' }, // iOS blue shades
  fontSizeButtonText: { color: isDarkMode ? '#ccc' : '#333', fontWeight: '500', fontSize: BASE_FONT_SIZES.fontSizeButtonText * fontSizeMultiplier }, // Adjusted default text color
  fontSizeButtonTextSelected: { color: '#fff' },
  logoutButton: { flexDirection: 'row', backgroundColor: '#FF3B30', paddingVertical: 14, paddingHorizontal: 20, borderRadius: 10, alignItems: 'center', justifyContent: 'center', marginTop: 40, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.2, shadowRadius: 2 }, // iOS red, adjusted padding/margin/shadow
  logoutIcon: { marginRight: 10 },
  logoutButtonText: { color: '#fff', fontWeight: '600', fontSize: BASE_FONT_SIZES.logoutButtonText * fontSizeMultiplier }, // Bolder text
  appVersion: { fontSize: BASE_FONT_SIZES.appVersion * fontSizeMultiplier, color: isDarkMode ? '#888' : '#777' }, // Dimmer version text
  sliderRow: { justifyContent: 'flex-start', paddingVertical: 8, paddingHorizontal: 15 }, // Consistent padding, more vertical space
  slider: { flex: 1, height: 40, marginLeft: 15 }, // Adjusted margin
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
    marginTop: 20,
  },
});

export default SettingsScreen;
