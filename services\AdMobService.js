import { Platform } from 'react-native';

// Try to import native ads, fall back gracefully if not available
let mobileAds, BannerAd, BannerAdSize, InterstitialAd, AdEventType;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  mobileAds = GoogleMobileAds.default;
  BannerAd = GoogleMobileAds.BannerAd;
  BannerAdSize = GoogleMobileAds.BannerAdSize;
  InterstitialAd = GoogleMobileAds.InterstitialAd;
  AdEventType = GoogleMobileAds.AdEventType;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, using fallback');
  mobileAds = null;
  BannerAd = null;
  BannerAdSize = null;
  InterstitialAd = null;
  AdEventType = null;
}

// Ad unit IDs with test ads for development
export const adUnitIds = {
  banner: Platform.select({
    ios: __DEV__ ? 'ca-app-pub-3940256099942544/2934735716' : 'ca-app-pub-9706687137550019/4124160377',
    android: __DEV__ ? 'ca-app-pub-3940256099942544/6300978111' : 'ca-app-pub-9706687137550019/4124160377',
  }),
  interstitial: Platform.select({
    ios: __DEV__ ? 'ca-app-pub-3940256099942544/4411468910' : 'ca-app-pub-9706687137550019/7998992050',
    android: __DEV__ ? 'ca-app-pub-3940256099942544/1033173712' : 'ca-app-pub-9706687137550019/7998992050',
  }),
};

// Initialize the Google Mobile Ads SDK with enhanced error handling and retry mechanism
export const initializeAdMob = (retryCount = 0) => {
  return new Promise((resolve, reject) => {
    // If native ads are not available, resolve immediately
    if (!mobileAds) {
      console.log('Native Google Mobile Ads not available, skipping initialization');
      resolve({ fallback: true });
      return;
    }

    const maxRetries = 2;
    const baseTimeout = 2000; // 2 seconds
    const currentTimeout = baseTimeout + (retryCount * 1000); // Progressive timeout

    try {
      console.log(`Initializing Google Mobile Ads SDK... (Attempt ${retryCount + 1}/${maxRetries + 1})`);

      const timeoutId = setTimeout(() => {
        console.warn(`AdMob initialization timed out after ${currentTimeout}ms (Attempt ${retryCount + 1})`);
        if (retryCount < maxRetries) {
          setTimeout(() => initializeAdMob(retryCount + 1).then(resolve).catch(reject), 1000);
        } else {
          resolve({ fallback: true, error: 'Initialization timeout after retries' });
        }
      }, currentTimeout);

      mobileAds()
        .initialize()
        .then(adapterStatuses => {
          clearTimeout(timeoutId);
          const readyAdapters = Object.entries(adapterStatuses).filter(([_, status]) => status.state === 1);
          const failedAdapters = Object.entries(adapterStatuses).filter(([_, status]) => status.state !== 1);

          if (readyAdapters.length > 0) {
            console.log('Google Mobile Ads SDK initialized successfully!');
            resolve({
              success: true,
              adapterStatuses,
              readyCount: readyAdapters.length,
              failedCount: failedAdapters.length
            });
          } else if (retryCount < maxRetries) {
            setTimeout(() => initializeAdMob(retryCount + 1).then(resolve).catch(reject), 1000);
          } else {
            resolve({ fallback: true, error: 'No adapters ready' });
          }
        })
        .catch(error => {
          clearTimeout(timeoutId);
          console.error(`AdMob initialization failed (Attempt ${retryCount + 1}):`, error);
          if (retryCount < maxRetries) {
            setTimeout(() => initializeAdMob(retryCount + 1).then(resolve).catch(reject), 1000);
          } else {
            resolve({ fallback: true, error: error.message });
          }
        });
    } catch (error) {
      console.error('Error during AdMob setup:', error);
      if (retryCount < maxRetries) {
        setTimeout(() => initializeAdMob(retryCount + 1).then(resolve).catch(reject), 1000);
      } else {
        resolve({ fallback: true, error: error.message });
      }
    }
  });
};

// Load and show interstitial ad with enhanced error handling
export const loadInterstitialAd = (onAdLoaded = () => {}, onAdDismissed = () => {}) => {
  if (!InterstitialAd || !AdEventType) {
    console.log('Interstitial ads not available, skipping');
    onAdDismissed();
    return null;
  }

  try {
    const interstitial = InterstitialAd.createForAdRequest(adUnitIds.interstitial, {
      requestNonPersonalizedAdsOnly: true,
      keywords: ['education', 'quiz', 'learning'],
    });

    const unsubscribeLoaded = interstitial.addAdEventListener(AdEventType.LOADED, () => {
      console.log('Interstitial ad loaded');
      onAdLoaded();
    });

    const unsubscribeClosed = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      console.log('Interstitial ad closed');
      onAdDismissed();
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    const unsubscribeError = interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
      console.error('Interstitial ad error:', error);
      onAdDismissed();
      unsubscribeLoaded();
      unsubscribeClosed();
      unsubscribeError();
    });

    interstitial.load();
    return interstitial;
  } catch (error) {
    console.error('Failed to create interstitial ad:', error);
    onAdDismissed();
    return null;
  }
};

export { BannerAd, BannerAdSize };